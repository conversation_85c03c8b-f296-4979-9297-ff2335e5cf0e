import { ReadPreference } from 'mongodb';
import { singleton } from 'tsyringe';

import {
    AdminSearchOrganizationsOrganizationDto,
    AdminSearchOrganizationsQueryDto,
    AdminSearchOrganizationsResponseDto,
} from '@malou-io/package-dto';

import { toDiacriticInsensitiveRegexString } from ':helpers/utils';
import OrganizationsRepository from ':modules/organizations/organizations.repository';

@singleton()
export class AdminSearchOrganizationsUseCase {
    constructor(private readonly _organizationsRepository: OrganizationsRepository) {}

    async execute(params: AdminSearchOrganizationsQueryDto): Promise<AdminSearchOrganizationsResponseDto> {
        const { text, limit = 20, offset = 0 } = params;

        const trimmedText = text?.trim();

        if (trimmedText) {
            // When we have a text filter, we can apply it before lookups since we only search in organization name
            return this._executeWithTextFilter(trimmedText, limit, offset);
        } else {
            // When no text filter, we can optimize by applying pagination before expensive lookups
            return this._executeWithoutTextFilter(limit, offset);
        }
    }

    private async _executeWithTextFilter(text: string, limit: number, offset: number): Promise<AdminSearchOrganizationsResponseDto> {
        const pipeline: any[] = [];

        // Apply text filter first since we only search in organization name (no need for lookups first)
        const searchRegex = toDiacriticInsensitiveRegexString(text);
        pipeline.push({
            $match: {
                name: { $regex: searchRegex, $options: 'i' },
            },
        });

        // Sort by creation date (newest first)
        pipeline.push({
            $sort: {
                createdAt: -1,
                name: 1,
            },
        });

        // Create count pipeline before adding lookups
        const countPipeline = [...pipeline];
        countPipeline.push({ $count: 'total' });

        // Add pagination
        pipeline.push({ $skip: offset });
        pipeline.push({ $limit: limit });

        // Now do the expensive lookups only on the filtered and paginated result set
        pipeline.push({
            $lookup: {
                from: 'users',
                localField: '_id',
                foreignField: 'organizationIds',
                as: 'users',
                pipeline: [
                    {
                        $project: {
                            _id: 1,
                            name: 1,
                            lastname: 1,
                            email: 1,
                            organizationIds: 1,
                        },
                    },
                ],
            },
        });

        pipeline.push({
            $lookup: {
                from: 'restaurants',
                localField: '_id',
                foreignField: 'organizationId',
                as: 'restaurants',
                pipeline: [
                    {
                        $match: { active: true },
                    },
                    {
                        $project: {
                            _id: 1,
                            name: 1,
                            active: 1,
                        },
                    },
                ],
            },
        });

        const [organizations, countResult] = await Promise.all([
            this._organizationsRepository.aggregate(pipeline, {
                readPreference: ReadPreference.SECONDARY_PREFERRED,
                comment: 'adminSearchOrganizationsWithText',
            }),
            this._organizationsRepository.aggregate(countPipeline, {
                readPreference: ReadPreference.SECONDARY_PREFERRED,
                comment: 'adminSearchOrganizationsWithTextCount',
            }),
        ]);

        const total = countResult.length > 0 ? countResult[0].total : 0;

        return {
            data: organizations.map((organization) => this._toDto(organization)),
            total,
        };
    }

    private async _executeWithoutTextFilter(limit: number, offset: number): Promise<AdminSearchOrganizationsResponseDto> {
        // First, get the total count without expensive lookups
        const totalCountResult = await this._organizationsRepository.aggregate([{ $count: 'total' }], {
            readPreference: ReadPreference.SECONDARY_PREFERRED,
            comment: 'adminSearchOrganizationsCountOnly',
        });
        const total = totalCountResult.length > 0 ? totalCountResult[0].total : 0;

        // Then get only the organizations we need with pagination applied first
        const pipeline: any[] = [];

        // Sort by creation date (newest first)
        pipeline.push({
            $sort: {
                createdAt: -1,
                name: 1,
            },
        });

        // Add pagination
        pipeline.push({ $skip: offset });
        pipeline.push({ $limit: limit });

        // Now do the expensive lookups only on the limited result set
        pipeline.push({
            $lookup: {
                from: 'users',
                localField: '_id',
                foreignField: 'organizationIds',
                as: 'users',
                pipeline: [
                    {
                        $project: {
                            _id: 1,
                            name: 1,
                            lastname: 1,
                            email: 1,
                            organizationIds: 1,
                        },
                    },
                ],
            },
        });

        pipeline.push({
            $lookup: {
                from: 'restaurants',
                localField: '_id',
                foreignField: 'organizationId',
                as: 'restaurants',
                pipeline: [
                    {
                        $match: { active: true },
                    },
                    {
                        $project: {
                            _id: 1,
                            name: 1,
                            active: 1,
                        },
                    },
                ],
            },
        });

        const organizations = await this._organizationsRepository.aggregate(pipeline, {
            readPreference: ReadPreference.SECONDARY_PREFERRED,
            comment: 'adminSearchOrganizationsWithoutText',
        });

        return {
            data: organizations.map((organization) => this._toDto(organization)),
            total,
        };
    }

    private _toDto(organization: any): AdminSearchOrganizationsOrganizationDto {
        return {
            _id: organization._id.toString(),
            name: organization.name,
            limit: organization.limit,
            verifiedEmailsForCampaigns: organization.verifiedEmailsForCampaigns || [],
            createdAt: organization.createdAt,
            updatedAt: organization.updatedAt,
            users:
                organization.users?.map((user: any) => ({
                    _id: user._id.toString(),
                    name: user.name,
                    lastname: user.lastname,
                    email: user.email,
                    organizationIds: user.organizationIds?.map((id: any) => id.toString()) || [],
                })) || [],
            restaurants:
                organization.restaurants?.map((restaurant: any) => ({
                    _id: restaurant._id.toString(),
                    name: restaurant.name,
                    active: restaurant.active,
                })) || [],
        };
    }
}
